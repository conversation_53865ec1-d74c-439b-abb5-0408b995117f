import { logger } from '@edutalent/commons-sdk';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import * as schedule from 'node-schedule';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { CommunicationChannel } from '@common/enums';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class OutgoingMessagesSendService implements OnModuleInit {
  constructor(
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
  ) {}

  async onModuleInit() {
    logger.info('Starting Jobs to send messages for each customer phone/channel...');

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll();

    for (const customerPhone of customerPhoneEntities) {
      if (
        customerPhone.communicationChannel === CommunicationChannel.BLIP_COLINA ||
        customerPhone.communicationChannel === CommunicationChannel.LOVELACE
      ) {
        logger.info(
          `Channel ${
            customerPhone.communicationChannel
          } does not require a job to outgoing messages. So skipping customer phone outgoing job: ${JSON.stringify(
            customerPhone,
          )} creation...`,
        );
        continue;
      }
      const job = schedule.scheduleJob(
        `${customerPhone.phoneNumber}_${customerPhone.communicationChannel}_outgoing`,
        customerPhone.outgoingCron,
        async () => {
          await CorrelationContextService.runBackgroundJob(
            'MESSAGE_HUB_JOB',
            `outgoingMessages-${customerPhone.phoneNumber}-${customerPhone.communicationChannel}`,
            async () => {
              // await this.outgoingMessageUseCase.sendPendingMessage(
              //   customerPhone.phoneNumber,
              //   customerPhone.communicationChannel,
              // );
            },
          );
        },
      );
      logger.info(
        `Job to send outgoing messages for phone: ${customerPhone.phoneNumber} of customer: ${
          customerPhone.customerId
        } with cron expression: ${customerPhone.incomingCron} for channel: ${
          customerPhone.communicationChannel
        } and will start running at: ${job.nextInvocation()}`,
      );
    }
  }
}
