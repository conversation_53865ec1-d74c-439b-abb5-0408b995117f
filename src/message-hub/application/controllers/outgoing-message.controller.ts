import { Body, Controller, Get, Param, Post, Req, Version } from '@nestjs/common';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { SystemAuthnGuard } from '@common/auth/system-authn.guard';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { CommunicationChannel } from '@common/enums';
import { ApiBearerAuth, ApiBody } from '@nestjs/swagger';


class SendOutgoingMessageDTO {
  customerPhoneNumber: string;
  communicationChannel: CommunicationChannel;
}
@ExcludeGuards(
  SystemAuthnGuard.name,
  AuthnGuard.name,
  AuthzAccountGuard.name,
  AuthzUserInAccountGuard.name,
)
@Controller('message-hub/outgoing-messages')
export class OutgoingMessageController {
  constructor(private readonly outgoingMessageUseCase: OutgoingMessageUseCase) {}

  @Get('/customer/:customerId/:to')
  @Version('1')
  async getOutgoingMessagesByCustomerId(
    @Param('customerId') customerId: string,
    @Param('to') to: string,
  ): Promise<any> {
    const messages = await this.outgoingMessageUseCase.getOutgoingMessagesByCustomerId(
      customerId,
      to,
    );

    return {
      statusCode: 200,
      data: messages,
    };
  }

  @ApiBody({
    type: SendOutgoingMessageDTO,
    description: 'Data to send outgoing messages',
    examples: {
      withCustomerPhoneNumber: {
        summary: 'Send outgoing messages to a customer phone number',
        value: {
          customerPhoneNumber: '*************',
          communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        },
      },
    },
  })
  @ApiBearerAuth()
  @Post('/send')
  @Version('1')
  async sendOutgoingMessages(@Body() sendOutgoingMessageDTO: SendOutgoingMessageDTO
  ): Promise<void> {
    await this.outgoingMessageUseCase.sendPendingMessage(
      sendOutgoingMessageDTO.customerPhoneNumber,
      sendOutgoingMessageDTO.communicationChannel,
    );
  }
}
